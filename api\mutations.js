import { gql } from '@apollo/client';

export const LOGIN_MUTATION = gql`
  mutation Login($username: String!, $password: String!) {
    login(username: $username, password: $password) {
      success
      agent {
        hiveId
        username
        role
        clearance
      }
    }
  }
`;

export const LOGOUT_MUTATION = gql`
  mutation Logout {
    logout
  }
`;

export const CREATE_ORGANIZATION = gql`
  mutation CreateOrganization($name: String!, $foundingDate: Date!) {
    createOrganization(name: $name, foundingDate: $foundingDate) {
      hiveId
      name
      foundingDate
    }
  }
`;

export const CREATE_PERSON = gql`
  mutation CreatePerson($firstName: String!, $lastName: String!, $dateOfBirth: Date!) {
    createPerson(firstName: $firstName, lastName: $lastName, dateOfBirth: $dateOfBirth) {
      hiveId
      firstName
      lastName
      dateOfBirth
    }
  }
`;

export const CREATE_CASE = gql`
  mutation CreateCase($title: String!) {
    createCase(title: $title) {
      hiveId
      title
      creationDate
    }
  }
`;

export const CREATE_AGENT = gql`
  mutation CreateAgent(
    $username: String!
    $password: String!
    $role: String!
    $backingPersonHiveId: ID!
    $clearance: ClearanceLevel
  ) {
    createAgent(
      username: $username
      password: $password
      role: $role
      backingPersonHiveId: $backingPersonHiveId
      clearance: $clearance
    ) {
      hiveId
      username
      role
      clearance
      backingPerson {
        hiveId
        firstName
        lastName
      }
    }
  }
`;

export const CREATE_INFORMANT = gql`
  mutation CreateInformant($codeName: String!, $backingPersonHiveId: ID!) {
    createInformant(codeName: $codeName, backingPersonHiveId: $backingPersonHiveId) {
      hiveId
      codeName
      backingPerson {
        hiveId
        firstName
        lastName
      }
    }
  }
`;

export const DELETE_PERSON = gql`
  mutation DeletePerson($hiveId: ID!) {
    deletePerson(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const DELETE_ORGANIZATION = gql`
  mutation DeleteOrganization($hiveId: ID!) {
    deleteOrganization(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const DELETE_CASE = gql`
  mutation DeleteCase($hiveId: ID!) {
    deleteCase(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const UPDATE_CASE = gql`
  mutation UpdateCase($hiveId: ID!, $title: String!, $status: CaseStatus) {
    updateCase(hiveId: $hiveId, title: $title, status: $status) {
      hiveId
      title
      creationDate
      status
    }
  }
`;

export const ADD_SUSPECT_TO_CASE = gql`
  mutation AddSuspectToCase($personHiveId: ID!, $caseHiveId: ID!) {
    addSuspectToCase(personHiveId: $personHiveId, caseHiveId: $caseHiveId) {
      hiveId
      firstName
      lastName
    }
  }
`;

export const REMOVE_SUSPECT_FROM_CASE = gql`
  mutation RemoveSuspectFromCase($personHiveId: ID!, $caseHiveId: ID!) {
    removeSuspectFromCase(personHiveId: $personHiveId, caseHiveId: $caseHiveId) {
      hiveId
      firstName
      lastName
    }
  }
`;

export const ADD_VICTIM_TO_CASE = gql`
  mutation AddVictimToCase($personHiveId: ID!, $caseHiveId: ID!) {
    addVictimToCase(personHiveId: $personHiveId, caseHiveId: $caseHiveId) {
      hiveId
      firstName
      lastName
    }
  }
`;

export const REMOVE_VICTIM_FROM_CASE = gql`
  mutation RemoveVictimFromCase($personHiveId: ID!, $caseHiveId: ID!) {
    removeVictimFromCase(personHiveId: $personHiveId, caseHiveId: $caseHiveId) {
      hiveId
      firstName
      lastName
    }
  }
`;

export const ADD_WITNESS_TO_CASE = gql`
  mutation AddWitnessToCase($personHiveId: ID!, $caseHiveId: ID!) {
    addWitnessToCase(personHiveId: $personHiveId, caseHiveId: $caseHiveId) {
      hiveId
      firstName
      lastName
    }
  }
`;

export const REMOVE_WITNESS_FROM_CASE = gql`
  mutation RemoveWitnessFromCase($personHiveId: ID!, $caseHiveId: ID!) {
    removeWitnessFromCase(personHiveId: $personHiveId, caseHiveId: $caseHiveId) {
      hiveId
      firstName
      lastName
    }
  }
`;

export const DELETE_AGENT = gql`
  mutation DeleteAgent($hiveId: ID!) {
    deleteAgent(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const SET_AGENT_CLEARANCE = gql`
  mutation SetAgentClearance($agentHiveId: ID!, $clearance: ClearanceLevel!) {
    setAgentClearance(agentHiveId: $agentHiveId, clearance: $clearance) {
      hiveId
      clearance
    }
  }
`;

export const SET_AGENT_ROLE = gql`
  mutation SetAgentRole($agentHiveId: ID!, $role: AgentRole!) {
    setAgentRole(agentHiveId: $agentHiveId, role: $role) {
      hiveId
      role
    }
  }
`;

export const UPDATE_AGENT = gql`
  mutation UpdateAgent($hiveId: ID!, $username: String, $password: String, $role: AgentRole, $clearance: ClearanceLevel) {
    updateAgent(hiveId: $hiveId, username: $username, password: $password, role: $role, clearance: $clearance) {
      hiveId
      username
      role
      clearance
      backingPerson {
        hiveId
        firstName
        lastName
      }
    }
  }
`;

export const DELETE_INFORMANT = gql`
  mutation DeleteInformant($hiveId: ID!) {
    deleteInformant(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const DELETE_VEHICLE = gql`
  mutation DeleteVehicle($hiveId: ID!) {
    deleteVehicle(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const CREATE_VEHICLE = gql`
  mutation CreateVehicle($type: VehicleType!, $make: String!, $model: String!, $color: String!) {
    createVehicle(type: $type, make: $make, model: $model, color: $color) {
      hiveId
      type
      make
      model
      color
    }
  }
`;

export const SET_VEHICLE_OWNER = gql`
  mutation SetVehicleOwner($vehicleHiveId: ID!, $ownerHiveId: ID!) {
    setVehicleOwner(vehicleHiveId: $vehicleHiveId, ownerHiveId: $ownerHiveId) {
      hiveId
      type
      make
      model
      color
      owner {
        __typename
        ... on Person {
          hiveId
          firstName
          lastName
        }
        ... on Organization {
          hiveId
          name
          foundingDate
        }
      }
    }
  }
`;

export const SET_INFORMANT_HANDLER = gql`
  mutation SetInformantHandler($informantHiveId: ID!, $agentHiveId: ID!) {
    setInformantHandler(informantHiveId: $informantHiveId, agentHiveId: $agentHiveId) {
      hiveId
      codeName
      handlerAgent {
        hiveId
        username
      }
    }
  }
`;

export const SET_ORGANIZATION_PARENT = gql`
  mutation SetOrganizationParent($childOrganizationHiveId: ID!, $parentOrganizationHiveId: ID!) {
    setOrganizationParent(childOrganizationHiveId: $childOrganizationHiveId, parentOrganizationHiveId: $parentOrganizationHiveId) {
      hiveId
      name
      parentOrganization {
        hiveId
        name
      }
      childOrganizations {
        hiveId
        name
      }
    }
  }
`;

export const CREATE_OPERATION = gql`
  mutation CreateOperation($title: String!, $type: OperationType!) {
    createOperation(title: $title, type: $type) {
      hiveId
      title
      type
      creationDate
    }
  }
`;

export const DELETE_OPERATION = gql`
  mutation DeleteOperation($hiveId: ID!) {
    deleteOperation(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const ADD_TARGET_TO_OPERATION = gql`
  mutation AddTargetToOperation($targetHiveId: ID!, $operationHiveId: ID!) {
    addTargetToOperation(targetHiveId: $targetHiveId, operationHiveId: $operationHiveId) {
      hiveId
      title
      type
      targets {
        __typename
        ... on Person {
          hiveId
          firstName
          lastName
        }
        ... on Organization {
          hiveId
          name
        }
        ... on Vehicle {
          hiveId
          type
          make
          model
          color
        }
      }
    }
  }
`;

export const REMOVE_TARGET_FROM_OPERATION = gql`
  mutation RemoveTargetFromOperation($targetHiveId: ID!, $operationHiveId: ID!) {
    removeTargetFromOperation(targetHiveId: $targetHiveId, operationHiveId: $operationHiveId) {
      hiveId
      title
      type
      targets {
        __typename
        ... on Person {
          hiveId
          firstName
          lastName
        }
        ... on Organization {
          hiveId
          name
        }
        ... on Vehicle {
          hiveId
          type
          make
          model
          color
        }
      }
    }
  }
`;

export const SET_LEAD_AGENT_FOR_OPERATION = gql`
  mutation SetLeadAgentForOperation($agentHiveId: ID!, $operationHiveId: ID!) {
    setLeadAgentForOperation(agentHiveId: $agentHiveId, operationHiveId: $operationHiveId) {
      hiveId
      title
      type
      leadAgent {
        hiveId
        username
        backingPerson {
          hiveId
          firstName
          lastName
        }
      }
    }
  }
`;

export const REMOVE_LEAD_AGENT_FROM_OPERATION = gql`
  mutation RemoveLeadAgentFromOperation($operationHiveId: ID!) {
    removeLeadAgentFromOperation(operationHiveId: $operationHiveId) {
      hiveId
      title
      type
      leadAgent {
        hiveId
        username
        backingPerson {
          hiveId
          firstName
          lastName
        }
      }
    }
  }
`;

export const ADD_SCOPED_CASE_TO_OPERATION = gql`
  mutation AddScopedCaseToOperation($operationHiveId: ID!, $caseHiveId: ID!) {
    addScopedCaseToOperation(operationHiveId: $operationHiveId, caseHiveId: $caseHiveId) {
      hiveId
      title
      type
      scopedToCase {
        hiveId
        title
        status
        creationDate
      }
    }
  }
`;

export const REMOVE_SCOPED_CASE_FROM_OPERATION = gql`
  mutation RemoveScopedCaseFromOperation($operationHiveId: ID!, $caseHiveId: ID!) {
    removeScopedCaseFromOperation(operationHiveId: $operationHiveId, caseHiveId: $caseHiveId) {
      hiveId
      title
      type
      scopedToCase {
        hiveId
        title
        status
        creationDate
      }
    }
  }
`;

export const CREATE_TASK = gql`
  mutation CreateTask(
    $title: String!
    $level: TaskLevel!
    $description: String
    $priority: TaskPriority
    $assignedAgentHiveId: ID
  ) {
    createTask(
      title: $title
      level: $level
      description: $description
      priority: $priority
      assignedAgentHiveId: $assignedAgentHiveId
    ) {
      hiveId
      title
      level
      priority
      description
      createdAt
    }
  }
`;

export const DELETE_TASK = gql`
  mutation DeleteTask($hiveId: ID!) {
    deleteTask(hiveId: $hiveId) {
      hiveId
      deleted
    }
  }
`;

export const ASSIGN_AGENT_TO_TASK = gql`
  mutation AssignAgentToTask($taskHiveId: ID!, $agentHiveId: ID!) {
    assignAgentToTask(taskHiveId: $taskHiveId, agentHiveId: $agentHiveId) {
      hiveId
      assignedAgent {
        hiveId
        username
      }
    }
  }
`;

export const REMOVE_AGENT_FROM_TASK = gql`
  mutation RemoveAgentFromTask($taskHiveId: ID!) {
    removeAgentFromTask(taskHiveId: $taskHiveId) {
      hiveId
      assignedAgent {
        hiveId
        username
      }
    }
  }
`;

export const SET_TASK_PRIORITY = gql`
  mutation SetTaskPriority($taskHiveId: ID!, $priority: TaskPriority!) {
    setTaskPriority(taskHiveId: $taskHiveId, priority: $priority) {
      hiveId
      priority
    }
  }
`;

export const SET_TASK_LEVEL = gql`
  mutation SetTaskLevel($taskHiveId: ID!, $level: TaskLevel!) {
    setTaskLevel(taskHiveId: $taskHiveId, level: $level) {
      hiveId
      level
    }
  }
`;

export const SET_TASK_SCOPE = gql`
  mutation SetTaskScope($taskHiveId: ID!, $scopeHiveId: ID!) {
    setTaskScope(taskHiveId: $taskHiveId, scopeHiveId: $scopeHiveId) {
      hiveId
      scope {
        __typename
        ... on Case {
          hiveId
          title
        }
        ... on Operation {
          hiveId
          title
        }
      }
    }
  }
`;
