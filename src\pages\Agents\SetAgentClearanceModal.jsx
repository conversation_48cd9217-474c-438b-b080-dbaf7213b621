import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { SET_AGENT_CLEARANCE } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';

const CLEARANCE_LEVELS = [
  'CL1', 'CL2', 'CL3', 'CL4', 'CL5',
  'CL6', 'CL7', 'CL8', 'CL9', 'CLS', 'CLX'
];

const SetAgentClearanceModal = ({ isOpen, onClose, agent, onRefetch }) => {
  const [clearance, setClearance] = useState('CL2');
  const [errorMessage, setErrorMessage] = useState('');

  const [setClearanceMutation, { loading }] = useMutation(SET_AGENT_CLEARANCE, {
    onCompleted: () => {
      onRefetch();
      onClose();
      setErrorMessage('');
      toast.success('Clearance updated successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not set clearance. Please try again.');
      toast.error(error.message || 'Could not set clearance. Please try again.');
    }
  });

  if (!isOpen || !agent) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    setClearanceMutation({ variables: { agentHiveId: agent.hiveId, clearance } });
  };

  const handleClose = () => {
    setErrorMessage('');
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>&times;</button>
        <div className={styles.modalHeader}>
          <h3>Set Agent Clearance</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Agent: {agent.username}
          </p>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            <div className={styles.formGroup}>
              <label htmlFor="agentClearance">Clearance Level:</label>
              <select
                id="agentClearance"
                className={styles.darkInput}
                value={clearance}
                onChange={(e) => setClearance(e.target.value)}
                required
              >
                {CLEARANCE_LEVELS.map(level => (
                  <option key={level} value={level}>{level}</option>
                ))}
              </select>
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Saving...' : 'Set Clearance'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SetAgentClearanceModal;
